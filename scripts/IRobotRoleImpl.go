package scripts

import (
	automated "chaossrv/internal"
	"chaossrv/internal/config"
	"chaossrv/internal/robot"
	conn "chaossrv/internal/robot/socket"
	"chaossrv/mocktables"
	"chaossrv/scripts/role_cmm"
	"chaossrv/scripts/role_gm"
	"chaossrv/scripts/role_hall"
	"chaossrv/scripts/role_login"
	"chaossrv/scripts/role_msg"
	"chaossrv/scripts/role_rank"
	"chaossrv/scripts/role_spot"
	"chaossrv/scripts/role_task"
	"chaossrv/scripts/role_world"
	"errors"
	"time"

	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	"github.com/robfig/cron"
	"github.com/sirupsen/logrus"
	"github.com/spf13/viper"
)

type RoleScript struct {
	TargetRobot    *robot.Robot
	ClientNtfCh    chan automated.StateHandle
	OrderID        int64
	IsRunning      bool
	IsLoginSendYet bool
	Cron           *cron.Cron
}

func (s *RoleScript) Init(rb *robot.Robot) error {
	s.Cron = cron.New()
	s.Cron.AddFunc("@every 1s", s.Update)
	s.Cron.Start()
	return nil
}

// connect 发起连接
func (s *RoleScript) connect(rb *robot.Robot) error {
	// 获取连接地址
	var pickFirstUrl string
	if viper.IsSet("st_tcp") {
		pickFirstUrl = viper.GetString("st_tcp")
	} else {
		appUpdateReq := mocktables.AppUpdate()
		logrus.Infof("[Chaos]AppUpdate回包：%v", appUpdateReq)
		pickFirstUrl = appUpdateReq.InfoAddress.GetSrvUri()[0]
		viper.Set("st_tcp", pickFirstUrl)
	}

	client := conn.NewSocket(rb.DeviceCode, rb.ClientNtfCh)
	if client == nil {
		return errors.New("创建失败：" + rb.DeviceCode)
	}

	// 绑定connect
	rb.Client = client
	return nil
}

func (s *RoleScript) Login(rb *robot.Robot) error {
	logHttp := logrus.WithFields(logrus.Fields{
		"No":          s.TargetRobot.SerialNo,
		"DeviceCode:": s.TargetRobot.DeviceCode,
	})

	err := s.connect(rb)
	if err != nil {
		logHttp.Errorf("[%s]NewClient失败", err.Error())
		return err
	}

	// 放在登录成功，有了Active的Client连接后,再挂载Role，这里后面可以动态添加
	role_login.InitLogin(rb)

	// 延迟只需
	time.AfterFunc(config.TimeLoginDelay, func() {
		logHttp.Infof("login send")
		rb.Report.LoginTimeS = time.Now().UnixMilli()
		loginResBody := mocktables.Login()
		loginResBody.DeviceInfo.DeviceCode = rb.DeviceCode
		rb.Client.SendPbMsg(int(commonPB.MsgID_CMD_LOGIN_REQ), loginResBody)
	})

	// 删除账号
	// time.AfterFunc(config.TimeLoginDelay, func() {
	// 	delReq := mocktables.DeleteAccount(1)
	// 	rb.Client.SendPbMsg(int(commonPB.MsgID_CMD_DELETE_ACCOUNT_REQ), delReq)
	// })

	return nil
}

func (s *RoleScript) Play(rb *robot.Robot) error {
	// 登录压测模式 - 只测试登录注册，不执行后续游戏逻辑
	logrus.Infof("机器人 %d 登录成功，开始执行压测逻辑", rb.UID)

	// 可以在这里添加登录后的简单验证逻辑
	// 比如获取玩家信息、进入大厅等基础操作
	s.LoginStressTest(rb)

	return nil
}

// LoginStressTest 登录压测逻辑
func (s *RoleScript) LoginStressTest(rb *robot.Robot) {
	// 登录成功后的基本验证操作
	// 这里可以添加一些轻量级的操作来验证登录状态

	// 1. 可选：获取玩家基本信息验证登录状态
	// 2. 可选：发送心跳包保持连接
	// 3. 统计登录成功数据

	logrus.Debugf("机器人 %d (设备码: %s) 登录压测完成", rb.UID, rb.DeviceCode)
}

func (s *RoleScript) LoopPlay(rb *robot.Robot) {
	// 挂载hall协议
	role_hall.InitHall(rb)
	// 钓点服初始化
	role_spot.InitSpot(rb)

	// 全局代理
	role_cmm.InitGlobal(rb)

	// role_loop.LoopHallReq(rb)
	logrus.Debugf("robot:%d, enter hall done", rb.UID)

	// time.AfterFunc(10*time.Second, func() {
	// 	// 挂载spot协议
	// 	role_spot.InitSpot(rb)
	// 	// 钓点服请求
	// 	role_loop.LoopHookFish(rb)
	// })
}

func (s *RoleScript) GmPlay(rb *robot.Robot) {
	role_gm.InitGm(rb)

	// role_gm.GmPlay(rb)
	// role_gm.GmSendMail(rb)
}

func (s *RoleScript) HallPlay(rb *robot.Robot) {
	// 挂载hall协议
	role_hall.InitHall(rb)

	// 全局代理
	role_cmm.InitGlobal(rb)

	// 大厅服请求
	role_hall.HallPlay(rb)
}

func (s *RoleScript) SpotPlay(rb *robot.Robot) {
	// 挂载Hall协议
	role_spot.InitSpot(rb)
	// 钓点服请求
	role_spot.SpotPlay(rb)
}

func (s *RoleScript) WorldPlay(rb *robot.Robot) {
	// 挂载Hall协议
	role_world.InitWorld(rb)

	// 钓点服请求
	role_world.WorldPlay(rb)
}

func (s *RoleScript) TaskPlay(rb *robot.Robot) {
	// 挂载Hall协议
	role_task.InitTask(rb)

	// 钓点服请求
	role_task.TaskPlay(rb)
}

func (s *RoleScript) MsgPlay(rb *robot.Robot) {
	role_msg.InitMsg(rb)

	role_msg.MsgPlay(rb)
}

func (s *RoleScript) RankPlayer(rb *robot.Robot) {
	role_rank.InitRank(rb)

	role_rank.RankPlay(rb)
}

func (s *RoleScript) Update() {
	// 这里可以改变一些参数模拟动态随机执行效果
	// robot := s.TargetRobot
	// if !s.TargetRobot.Client.IsConn {
	// 	for {
	// 		// 全部弹出
	// 		mock := robot.PopMock()
	// 		if mock == nil {
	// 			return
	// 		}
	// 		// 发送mock信息
	// 		logrus.Debugf("mock send:%d data:%+v", mock.MsgId, mock.Data)
	// 		robot.Client.SendPbMsg(mock.MsgId, mock.Data)
	// 	}
	// }

	// 临时使用简单的状态判断处理
	// TODO: 套一层行为树进行目标行动决策
	rb := s.TargetRobot
	if rb.State == robot.ROBOT_STATE_RUNNING && !rb.Client.IsConn() {
		rb.State = robot.ROBOT_STATE_DISCONNECT
	}

	switch rb.State {
	case robot.ROBOT_STATE_INIT:
		if rb.Client == nil {
			s.Login(rb)
		}

		// 发起连接/登录
	case robot.ROBOT_STATE_RUNNING:
	case robot.ROBOT_STATE_DISCONNECT:
		// 重置状态重连
		if viper.GetBool("reconnect") {
			s.reset()
			if rb.Client == nil {
				s.Login(rb)
			}
		}
	case robot.ROBOT_STATE_SHUTDOWN:
		// 关闭连接
		s.Shutdown()
	case robot.ROBOT_STATE_ANOTHER:
		// 关闭连接
		s.Another()
	}
}

func (s *RoleScript) reset() {
	rb := s.TargetRobot
	rb.State = robot.ROBOT_STATE_INIT
	rb.Client.Close()
	rb.Client = nil
	rb.UID = 0
	rb.LoginRsp = nil
}

// 暂停机器人
func (s *RoleScript) StopYet() {
	s.IsRunning = false
	s.Cron.Stop()

}

func (s *RoleScript) Shutdown() {
	s.IsRunning = false
	s.Cron.Stop()

	ret := automated.StateHandle{}
	ret.Code = automated.ConstCsLogOut
	ret.DeviceID = s.TargetRobot.DeviceCode
	ret.UID = s.TargetRobot.UID

	s.TargetRobot.ClientNtfCh <- ret

	// 主动下线 (临时处理)
	s.TargetRobot.Client.Close()
	s.TargetRobot.ClientNtfCh = nil
}

func (s *RoleScript) Another() {
	s.IsRunning = false
	s.Cron.Stop()

	// 主动下线 (临时处理)
	s.TargetRobot.Client.Close()
	s.TargetRobot.ClientNtfCh = nil
}
